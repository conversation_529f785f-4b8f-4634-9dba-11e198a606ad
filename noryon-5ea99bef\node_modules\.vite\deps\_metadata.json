{"hash": "cfae5f29", "configHash": "291a121a", "lockfileHash": "508a81e9", "browserHash": "85c5ec9b", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "5ce03704", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "d29ef163", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "381114b9", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "1acb36c0", "needsInterop": true}, "@anthropic-ai/sdk": {"src": "../../@anthropic-ai/sdk/index.mjs", "file": "@anthropic-ai_sdk.js", "fileHash": "da8e9b1b", "needsInterop": false}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "62f212e9", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "ea949355", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "04be0e6e", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "d37d936b", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "b04e5c93", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "28173df1", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "0bd78125", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "33295639", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "63d31257", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "c032be84", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "2cc34e9b", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "759d0d1c", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.mjs", "file": "date-fns.js", "fileHash": "a51c9106", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "c7649b0f", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "4641afe3", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "518ae194", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "da74bc04", "needsInterop": false}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "371a81bd", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "3e956b88", "needsInterop": false}, "xterm": {"src": "../../xterm/lib/xterm.js", "file": "xterm.js", "fileHash": "ee4004ae", "needsInterop": true}, "xterm-addon-fit": {"src": "../../xterm-addon-fit/lib/xterm-addon-fit.js", "file": "xterm-addon-fit.js", "fileHash": "e341c1a3", "needsInterop": true}, "xterm-addon-web-links": {"src": "../../xterm-addon-web-links/lib/xterm-addon-web-links.js", "file": "xterm-addon-web-links.js", "fileHash": "bf4b172c", "needsInterop": true}}, "chunks": {"browser-V2CCMWH4": {"file": "browser-V2CCMWH4.js"}, "chunk-NQPNIMY6": {"file": "chunk-NQPNIMY6.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-FOUULTH2": {"file": "chunk-FOUULTH2.js"}, "chunk-KH6RT5NZ": {"file": "chunk-KH6RT5NZ.js"}, "chunk-4JQ7FP6P": {"file": "chunk-4JQ7FP6P.js"}, "chunk-4EZH7PIZ": {"file": "chunk-4EZH7PIZ.js"}, "chunk-NRLRORVO": {"file": "chunk-NRLRORVO.js"}, "chunk-P6IYXBUH": {"file": "chunk-P6IYXBUH.js"}, "chunk-CHRXFEGR": {"file": "chunk-CHRXFEGR.js"}, "chunk-QEORNWQW": {"file": "chunk-QEORNWQW.js"}, "chunk-AFTKWWG3": {"file": "chunk-AFTKWWG3.js"}, "chunk-6BX2N2O2": {"file": "chunk-6BX2N2O2.js"}, "chunk-LPBYGXI2": {"file": "chunk-LPBYGXI2.js"}, "chunk-TH7NCS4R": {"file": "chunk-TH7NCS4R.js"}, "chunk-OT5EQO2H": {"file": "chunk-OT5EQO2H.js"}, "chunk-OU5AQDZK": {"file": "chunk-OU5AQDZK.js"}, "chunk-EWTE5DHJ": {"file": "chunk-EWTE5DHJ.js"}}}