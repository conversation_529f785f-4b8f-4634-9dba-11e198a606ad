import {
  __commonJS
} from "./chunk-EWTE5DHJ.js";

// node_modules/xterm-addon-web-links/lib/xterm-addon-web-links.js
var require_xterm_addon_web_links = __commonJS({
  "node_modules/xterm-addon-web-links/lib/xterm-addon-web-links.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "object" == typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define([], t) : "object" == typeof exports ? exports.WebLinksAddon = t() : e.WebLinksAddon = t();
    }(self, () => (() => {
      "use strict";
      var e = { 6: (e2, t2) => {
        Object.defineProperty(t2, "__esModule", { value: true }), t2.LinkComputer = t2.WebLinkProvider = void 0, t2.WebLinkProvider = class {
          constructor(e3, t3, n3, i2 = {}) {
            this._terminal = e3, this._regex = t3, this._handler = n3, this._options = i2;
          }
          provideLinks(e3, t3) {
            const i2 = n2.computeLink(e3, this._regex, this._terminal, this._handler);
            t3(this._addCallbacks(i2));
          }
          _addCallbacks(e3) {
            return e3.map((e4) => (e4.leave = this._options.leave, e4.hover = (t3, n3) => {
              if (this._options.hover) {
                const { range: i2 } = e4;
                this._options.hover(t3, n3, i2);
              }
            }, e4));
          }
        };
        class n2 {
          static computeLink(e3, t3, i2, r) {
            const o = new RegExp(t3.source, (t3.flags || "") + "g"), [s, a] = n2._getWindowedLineStrings(e3 - 1, i2), c = s.join("");
            let d;
            const l = [];
            for (; d = o.exec(c); ) {
              const e4 = d[0];
              try {
                const t5 = new URL(e4), n3 = decodeURI(t5.toString());
                if (e4 !== n3 && e4 + "/" !== n3) continue;
              } catch (e5) {
                continue;
              }
              const [t4, o2] = n2._mapStrIdx(i2, a, 0, d.index), [s2, c2] = n2._mapStrIdx(i2, t4, o2, e4.length);
              if (-1 === t4 || -1 === o2 || -1 === s2 || -1 === c2) continue;
              const p = { start: { x: o2 + 1, y: t4 + 1 }, end: { x: c2, y: s2 + 1 } };
              l.push({ range: p, text: e4, activate: r });
            }
            return l;
          }
          static _getWindowedLineStrings(e3, t3) {
            let n3, i2 = e3, r = e3, o = 0, s = "";
            const a = [];
            if (n3 = t3.buffer.active.getLine(e3)) {
              const e4 = n3.translateToString(true);
              if (n3.isWrapped && " " !== e4[0]) {
                for (o = 0; (n3 = t3.buffer.active.getLine(--i2)) && o < 2048 && (s = n3.translateToString(true), o += s.length, a.push(s), n3.isWrapped && -1 === s.indexOf(" ")); ) ;
                a.reverse();
              }
              for (a.push(e4), o = 0; (n3 = t3.buffer.active.getLine(++r)) && n3.isWrapped && o < 2048 && (s = n3.translateToString(true), o += s.length, a.push(s), -1 === s.indexOf(" ")); ) ;
            }
            return [a, i2];
          }
          static _mapStrIdx(e3, t3, n3, i2) {
            const r = e3.buffer.active, o = r.getNullCell();
            let s = n3;
            for (; i2; ) {
              const e4 = r.getLine(t3);
              if (!e4) return [-1, -1];
              for (let n4 = s; n4 < e4.length; ++n4) {
                e4.getCell(n4, o);
                const s2 = o.getChars();
                if (o.getWidth() && (i2 -= s2.length || 1, n4 === e4.length - 1 && "" === s2)) {
                  const e5 = r.getLine(t3 + 1);
                  e5 && e5.isWrapped && (e5.getCell(0, o), 2 === o.getWidth() && (i2 += 1));
                }
                if (i2 < 0) return [t3, n4];
              }
              t3++, s = 0;
            }
            return [t3, s];
          }
        }
        t2.LinkComputer = n2;
      } }, t = {};
      function n(i2) {
        var r = t[i2];
        if (void 0 !== r) return r.exports;
        var o = t[i2] = { exports: {} };
        return e[i2](o, o.exports, n), o.exports;
      }
      var i = {};
      return (() => {
        var e2 = i;
        Object.defineProperty(e2, "__esModule", { value: true }), e2.WebLinksAddon = void 0;
        const t2 = n(6), r = /https?:[/]{2}[^\s"'!*(){}|\\\^<>`]*[^\s"':,.!?{}|\\\^~\[\]`()<>]/;
        function o(e3, t3) {
          const n2 = window.open();
          if (n2) {
            try {
              n2.opener = null;
            } catch (e4) {
            }
            n2.location.href = t3;
          } else console.warn("Opening link blocked as opener could not be cleared");
        }
        e2.WebLinksAddon = class {
          constructor(e3 = o, t3 = {}) {
            this._handler = e3, this._options = t3;
          }
          activate(e3) {
            this._terminal = e3;
            const n2 = this._options, i2 = n2.urlRegex || r;
            this._linkProvider = this._terminal.registerLinkProvider(new t2.WebLinkProvider(this._terminal, i2, this._handler, n2));
          }
          dispose() {
            var e3;
            null === (e3 = this._linkProvider) || void 0 === e3 || e3.dispose();
          }
        };
      })(), i;
    })());
  }
});
export default require_xterm_addon_web_links();
//# sourceMappingURL=xterm-addon-web-links.js.map
