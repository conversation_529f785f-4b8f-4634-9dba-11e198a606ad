import { useState, useCallback, useEffect } from 'react';
import {
  Code,
  Terminal as TerminalIcon,
  Play,
  Save,
  Folder,
  MessageSquare
} from 'lucide-react';

import { motion, AnimatePresence } from 'framer-motion';
import FileSystemManager from './FileSystemManager';
import CodeEditor from './CodeEditor';
import FileTabs from './FileTabs';
import WebTerminal from './WebTerminal';
import LivePreview from './LivePreview';
import AIChatAssistant from './AIChatAssistant';

const IDELayout = ({
  initialFiles = {},
  onFilesChange,
  onAIAssist,
  onRunCode,
  className = ''
}) => {
  const [fileStructure, setFileStructure] = useState(initialFiles);
  const [openFiles, setOpenFiles] = useState([]);
  const [activeFile, setActiveFile] = useState(null);
  const [unsavedFiles, setUnsavedFiles] = useState(new Set());
  
  // IDE-style layout states
  const [ideMode, setIdeMode] = useState('coding'); // 'coding', 'preview'
  const [fileExplorerWidth, setFileExplorerWidth] = useState(280);
  const [terminalHeight, setTerminalHeight] = useState(200);
  const [chatSidebarWidth, setChatSidebarWidth] = useState(350);
  const [showTerminal, setShowTerminal] = useState(true);
  const [showChatSidebar, setShowChatSidebar] = useState(true);
  const [isResizingExplorer, setIsResizingExplorer] = useState(false);
  const [isResizingTerminal, setIsResizingTerminal] = useState(false);
  const [isResizingChat, setIsResizingChat] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Responsive design effect for IDE layout
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const newIsMobile = width < 768;
      setIsMobile(newIsMobile);
      
      // Adjust layout for smaller screens
      if (newIsMobile) {
        setFileExplorerWidth(Math.min(240, width * 0.3));
        setTerminalHeight(150);
        setChatSidebarWidth(Math.min(280, width * 0.4));
        setShowTerminal(false); // Hide terminal on mobile by default
        setShowChatSidebar(false); // Hide chat on mobile initially
      } else {
        setFileExplorerWidth(280);
        setTerminalHeight(200);
        setChatSidebarWidth(350);
        setShowChatSidebar(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Initialize with first file if available
  useEffect(() => {
    const fileKeys = Object.keys(initialFiles);
    if (fileKeys.length > 0 && openFiles.length === 0) {
      const firstFile = fileKeys[0];
      setOpenFiles([firstFile]);
      setActiveFile(firstFile);
    }
  }, [initialFiles, openFiles.length]);

  // Handle file selection from file explorer
  const handleFileSelect = useCallback((filePath) => {
    if (!openFiles.includes(filePath)) {
      setOpenFiles(prev => [...prev, filePath]);
    }
    setActiveFile(filePath);
  }, [openFiles]);

  // Handle file close
  const handleFileClose = useCallback((filePath) => {
    setOpenFiles(prev => {
      const newOpenFiles = prev.filter(f => f !== filePath);
      
      // If closing active file, switch to another open file
      if (filePath === activeFile) {
        const currentIndex = prev.indexOf(filePath);
        const nextFile = newOpenFiles[currentIndex] || newOpenFiles[currentIndex - 1] || null;
        setActiveFile(nextFile);
      }
      
      return newOpenFiles;
    });
    
    // Remove from unsaved files
    setUnsavedFiles(prev => {
      const newSet = new Set(prev);
      newSet.delete(filePath);
      return newSet;
    });
  }, [activeFile]);

  // Handle close all files
  const handleFileCloseAll = useCallback(() => {
    setOpenFiles([]);
    setActiveFile(null);
    setUnsavedFiles(new Set());
  }, []);

  // Handle close other files
  const handleFileCloseOthers = useCallback((keepFile) => {
    setOpenFiles([keepFile]);
    setActiveFile(keepFile);
    setUnsavedFiles(prev => {
      const newSet = new Set();
      if (prev.has(keepFile)) {
        newSet.add(keepFile);
      }
      return newSet;
    });
  }, []);

  // Handle file content change
  const handleFileChange = useCallback((newContent) => {
    if (activeFile) {
      setFileStructure(prev => ({
        ...prev,
        [activeFile]: newContent
      }));
      
      setUnsavedFiles(prev => new Set([...prev, activeFile]));
      onFilesChange?.({ ...fileStructure, [activeFile]: newContent });
    }
  }, [activeFile, fileStructure, onFilesChange]);

  // Handle file save
  const handleFileSave = useCallback((content, fileName) => {
    if (fileName) {
      setFileStructure(prev => ({
        ...prev,
        [fileName]: content
      }));
      
      setUnsavedFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(fileName);
        return newSet;
      });
      
      onFilesChange?.({ ...fileStructure, [fileName]: content });
    }
  }, [fileStructure, onFilesChange]);

  // Handle file creation
  const handleFileCreate = useCallback((filePath, content = '') => {
    setFileStructure(prev => ({
      ...prev,
      [filePath]: content
    }));
    
    // Open the new file
    if (!openFiles.includes(filePath)) {
      setOpenFiles(prev => [...prev, filePath]);
    }
    setActiveFile(filePath);
  }, [openFiles]);

  // Handle file deletion
  const handleFileDelete = useCallback((filePath) => {
    setFileStructure(prev => {
      const newStructure = { ...prev };
      delete newStructure[filePath];
      return newStructure;
    });
    
    // Close file if open
    handleFileClose(filePath);
  }, [handleFileClose]);

  // Handle file rename
  const handleFileRename = useCallback((oldPath, newPath) => {
    setFileStructure(prev => {
      const newStructure = { ...prev };
      newStructure[newPath] = newStructure[oldPath];
      delete newStructure[oldPath];
      return newStructure;
    });
    
    // Update open files
    setOpenFiles(prev => prev.map(f => f === oldPath ? newPath : f));
    
    // Update active file
    if (activeFile === oldPath) {
      setActiveFile(newPath);
    }
    
    // Update unsaved files
    setUnsavedFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(oldPath)) {
        newSet.delete(oldPath);
        newSet.add(newPath);
      }
      return newSet;
    });
  }, [activeFile]);

  // Handle folder creation
  const handleFolderCreate = useCallback((folderPath) => {
    // Create a placeholder file to represent the folder
    const placeholderFile = `${folderPath}/.gitkeep`;
    setFileStructure(prev => ({
      ...prev,
      [placeholderFile]: ''
    }));
  }, []);

  // Handle file structure update from AI
  const handleFileStructureUpdate = useCallback((newFiles) => {
    setFileStructure(newFiles);

    // Open the main file if available
    const mainFiles = ['index.html', 'src/App.jsx', 'src/main.jsx', 'App.js'];
    const fileToOpen = mainFiles.find(file => newFiles[file]) || Object.keys(newFiles)[0];

    if (fileToOpen) {
      if (!openFiles.includes(fileToOpen)) {
        setOpenFiles(prev => [fileToOpen, ...prev]);
      }
      setActiveFile(fileToOpen);
    }

    onFilesChange?.(newFiles);
  }, [openFiles, onFilesChange]);

  // Handle AI assist
  const handleAIAssist = useCallback((context) => {
    onAIAssist?.({
      ...context,
      fileStructure,
      openFiles,
      activeFile
    });
  }, [onAIAssist, fileStructure, openFiles, activeFile]);

  // Handle run code
  const handleRunCode = useCallback((code, fileName) => {
    onRunCode?.(code, fileName, fileStructure);
  }, [onRunCode, fileStructure]);

  // Handle terminal command with file system integration
  const handleTerminalCommand = useCallback(async (command) => {
    const args = command.split(' ');
    const cmd = args[0].toLowerCase();

    try {
      switch (cmd) {
        case 'ls': {
          // List current project files
          const files = Object.keys(fileStructure);
          if (files.length === 0) {
            return 'No files in project';
          }

          const directories = new Set();
          const fileList = [];

          files.forEach(file => {
            const parts = file.split('/');
            if (parts.length > 1) {
              directories.add(parts[0] + '/');
            } else {
              fileList.push(file);
            }
          });

          let result = '';
          if (directories.size > 0) {
            result += '📁 Directories:\n';
            Array.from(directories).forEach(dir => {
              result += `  ${dir}\n`;
            });
            result += '\n';
          }

          if (fileList.length > 0) {
            result += '📄 Files:\n';
            fileList.forEach(file => {
              result += `  ${file}\n`;
            });
          }

          return result;
        }

        case 'cat': {
          const fileName = args[1];
          if (!fileName) {
            return 'Usage: cat <filename>';
          }

          if (fileStructure[fileName]) {
            return `Contents of ${fileName}:\n\n${fileStructure[fileName]}`;
          } else {
            return `File not found: ${fileName}`;
          }
        }

        case 'touch': {
          const newFileName = args[1];
          if (!newFileName) {
            return 'Usage: touch <filename>';
          }

          handleFileCreate(newFileName, '');
          return `Created file: ${newFileName}`;
        }

        case 'rm': {
          const fileToDelete = args[1];
          if (!fileToDelete) {
            return 'Usage: rm <filename>';
          }

          if (fileStructure[fileToDelete]) {
            handleFileDelete(fileToDelete);
            return `Deleted file: ${fileToDelete}`;
          } else {
            return `File not found: ${fileToDelete}`;
          }
        }

        case 'wc': {
          const wcFileName = args[1];
          if (!wcFileName) {
            return 'Usage: wc <filename>';
          }

          if (fileStructure[wcFileName]) {
            const content = fileStructure[wcFileName];
            const lines = content.split('\n').length;
            const words = content.split(/\s+/).filter(w => w.length > 0).length;
            const chars = content.length;
            return `${lines} lines, ${words} words, ${chars} characters in ${wcFileName}`;
          } else {
            return `File not found: ${wcFileName}`;
          }
        }

        default: {
          // Fallback to simulated execution
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve(`Command executed: ${command}`);
            }, 1000);
          });
        }
      }
    } catch (error) {
      return `Error: ${error.message}`;
    }
  }, [fileStructure, handleFileCreate, handleFileDelete]);

  // Handle mode switching between coding and preview
  const handleModeSwitch = useCallback((newMode) => {
    setIdeMode(newMode);
    // Hide terminal when in preview mode
    if (newMode === 'preview') {
      setShowTerminal(false);
    }
  }, []);

  // Handle file explorer resize
  const handleExplorerResize = useCallback((e) => {
    if (isMobile) return;
    
    setIsResizingExplorer(true);
    const startX = e.clientX;
    const startWidth = fileExplorerWidth;

    const handleMouseMove = (e) => {
      const deltaX = e.clientX - startX;
      const newWidth = Math.max(200, Math.min(400, startWidth + deltaX));
      setFileExplorerWidth(newWidth);
    };

    const handleMouseUp = () => {
      setIsResizingExplorer(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [fileExplorerWidth, isMobile]);

  // Handle terminal resize
  const handleTerminalResize = useCallback((e) => {
    if (isMobile) return;

    setIsResizingTerminal(true);
    const startY = e.clientY;
    const startHeight = terminalHeight;

    const handleMouseMove = (e) => {
      const deltaY = startY - e.clientY;
      const newHeight = Math.max(120, Math.min(500, startHeight + deltaY));
      setTerminalHeight(newHeight);

      // Force terminal resize when panel height changes
      requestAnimationFrame(() => {
        const terminalElement = document.querySelector('.terminal-container .xterm');
        if (terminalElement) {
          // Trigger xterm.js fit addon to recalculate dimensions
          const event = new Event('resize');
          window.dispatchEvent(event);

          // Also trigger a custom event for the terminal component
          const customEvent = new CustomEvent('terminal-resize', {
            detail: { height: newHeight }
          });
          terminalElement.dispatchEvent(customEvent);
        }
      });
    };

    const handleMouseUp = () => {
      setIsResizingTerminal(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [terminalHeight, isMobile]);

  // Toggle terminal visibility
  const handleTerminalToggle = useCallback(() => {
    if (ideMode === 'coding') {
      setShowTerminal(!showTerminal);
    }
  }, [ideMode, showTerminal]);

  // Handle chat sidebar resize
  const handleChatResize = useCallback((e) => {
    if (isMobile) return;
    
    setIsResizingChat(true);
    const startX = e.clientX;
    const startWidth = chatSidebarWidth;

    const handleMouseMove = (e) => {
      const deltaX = startX - e.clientX;
      const newWidth = Math.max(250, Math.min(500, startWidth + deltaX));
      setChatSidebarWidth(newWidth);
    };

    const handleMouseUp = () => {
      setIsResizingChat(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [chatSidebarWidth, isMobile]);

  // Toggle chat sidebar visibility
  const handleChatToggle = useCallback(() => {
    setShowChatSidebar(!showChatSidebar);
  }, [showChatSidebar]);

  const currentFileContent = activeFile ? fileStructure[activeFile] || '' : '';

  return (
    <div className={`ide-container flex bg-black text-white ${className}`}>
      {/* IDE Styling */}
      <style>{`
        /* IDE-specific styling */
        .ide-panel {
          background: rgba(0,0,0,0.7);
          backdrop-filter: blur(12px);
          border: 1px solid rgba(255,255,255,0.08);
        }
        
        .ide-button {
          @apply rounded-md transition-all duration-150 ease-out;
          background: rgba(255,255,255,0.08);
          border: 1px solid rgba(255,255,255,0.05);
        }
        
        .ide-button:hover {
          background: rgba(255,255,255,0.12);
          border-color: rgba(255,255,255,0.1);
        }
        
        .ide-button:active {
          background: rgba(255,255,255,0.06);
        }
        
        .ide-button.active {
          background: rgba(255,255,255,0.15);
          border-color: rgba(255,255,255,0.2);
        }
        
        .resize-handle {
          transition: background-color 0.2s ease;
        }
        
        .resize-handle:hover {
          background: rgba(255,255,255,0.1);
        }
        
        .custom-scrollbar::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(255,255,255,0.03);
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(255,255,255,0.15);
          border-radius: 4px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(255,255,255,0.25);
        }
        
        /* Fix height and overflow issues */
        .ide-container {
          height: 100vh;
          overflow: hidden;
        }

        .scrollable-panel {
          overflow-y: auto;
          overflow-x: hidden;
          max-height: 100%;
          scroll-behavior: smooth;
        }

        .fixed-height {
          height: calc(100vh - 36px); /* Account for header */
          min-height: 0; /* Allow flex children to shrink */
        }

        /* Enhanced scrollbar styling for all scrollable areas */
        .scrollable-panel::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        .scrollable-panel::-webkit-scrollbar-track {
          background: rgba(255,255,255,0.03);
          border-radius: 4px;
        }

        .scrollable-panel::-webkit-scrollbar-thumb {
          background: rgba(255,255,255,0.15);
          border-radius: 4px;
          border: 1px solid rgba(255,255,255,0.05);
        }

        .scrollable-panel::-webkit-scrollbar-thumb:hover {
          background: rgba(255,255,255,0.25);
          border-color: rgba(255,255,255,0.1);
        }

        .scrollable-panel::-webkit-scrollbar-corner {
          background: transparent;
        }

        /* Ensure proper flex behavior for nested layouts */
        .flex-1 {
          min-height: 0; /* Critical for flex children with overflow */
          min-width: 0;
        }

        /* Terminal specific improvements */
        .terminal-panel {
          background: rgba(26, 26, 26, 0.95);
          backdrop-filter: blur(8px);
        }

        /* Smooth transitions for layout changes */
        .ide-panel {
          transition: all 0.2s ease-out;
        }

        /* Ensure proper text selection in IDE */
        .ide-container * {
          -webkit-user-select: text;
          -moz-user-select: text;
          -ms-user-select: text;
          user-select: text;
        }

        .ide-container button,
        .ide-container .cursor-pointer {
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
        }
      `}</style>
      
      {/* IDE Header */}
      <div className={`absolute top-0 left-0 right-0 ${isMobile ? 'h-10' : 'h-9'} bg-black/95 backdrop-blur-xl border-b border-white/08 flex items-center justify-between px-4 z-20`}>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-gradient-to-br from-white to-gray-300 rounded-sm flex items-center justify-center">
              <Code className="w-2.5 h-2.5 text-black" />
            </div>
            {!isMobile && <span className="text-xs font-medium text-white">IDE</span>}
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          {/* Mode Toggle */}
          <div className="flex items-center gap-1 bg-white/05 rounded-md p-0.5">
            <button
              onClick={() => handleModeSwitch('coding')}
              className={`px-3 py-1 text-xs rounded-sm transition-all ${
                ideMode === 'coding' 
                  ? 'bg-white/15 text-white' 
                  : 'text-gray-400 hover:text-white hover:bg-white/08'
              }`}
            >
              Code
            </button>
            <button
              onClick={() => handleModeSwitch('preview')}
              className={`px-3 py-1 text-xs rounded-sm transition-all ${
                ideMode === 'preview' 
                  ? 'bg-white/15 text-white' 
                  : 'text-gray-400 hover:text-white hover:bg-white/08'
              }`}
            >
              Preview
            </button>
          </div>
          
          {/* Chat Toggle */}
          <button
            onClick={handleChatToggle}
            className={`ide-button px-2 py-1 text-xs ${
              showChatSidebar ? 'active' : ''
            }`}
            title="Toggle AI Chat"
          >
            <MessageSquare className="w-3 h-3" />
          </button>

          {/* Terminal Toggle (only in coding mode) */}
          {ideMode === 'coding' && !isMobile && (
            <button
              onClick={handleTerminalToggle}
              className={`ide-button px-2 py-1 text-xs ${
                showTerminal ? 'active' : ''
              }`}
              title="Toggle Terminal"
            >
              <TerminalIcon className="w-3 h-3" />
            </button>
          )}
          
          {/* Quick Actions */}
          <div className="flex items-center gap-1">
            <button className="ide-button w-6 h-6 flex items-center justify-center" title="Save">
              <Save className="w-3 h-3" />
            </button>
            <button className="ide-button w-6 h-6 flex items-center justify-center" title="Run">
              <Play className="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>

      {/* IDE Main Layout */}
      <div className={`flex w-full fixed-height ${isMobile ? 'pt-10' : 'pt-9'}`}>
        {/* File Explorer - Only in coding mode */}
        <AnimatePresence>
          {ideMode === 'coding' && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: fileExplorerWidth, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: [0.23, 1, 0.32, 1] }}
              className="flex-shrink-0 ide-panel border-r border-white/08"
            >
              <div className="h-full flex flex-col">
                {/* Explorer Header */}
                <div className="px-3 py-2 border-b border-white/08">
                  <div className="flex items-center gap-2">
                    <Folder className="w-4 h-4 text-gray-400" />
                    <span className="text-xs font-medium text-gray-300">EXPLORER</span>
                  </div>
                </div>
                
                {/* File Tree */}
                <div className="flex-1 scrollable-panel custom-scrollbar">
                  <FileSystemManager
                    fileStructure={fileStructure}
                    onFileSelect={handleFileSelect}
                    onFileCreate={handleFileCreate}
                    onFileDelete={handleFileDelete}
                    onFileRename={handleFileRename}
                    onFolderCreate={handleFolderCreate}
                    activeFile={activeFile}
                    className="h-full"
                  />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Explorer Resize Handle */}
        {ideMode === 'coding' && !isMobile && (
          <div
            className={`w-1 bg-transparent hover:bg-white/10 cursor-col-resize resize-handle ${
              isResizingExplorer ? 'bg-white/20' : ''
            }`}
            onMouseDown={handleExplorerResize}
          />
        )}

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Editor/Preview Area */}
          <div className="flex-1 flex flex-col relative">
            <AnimatePresence mode="wait">
              {ideMode === 'coding' ? (
                <motion.div
                  key="coding"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="flex-1 flex flex-col ide-panel"
                >
                  {/* Editor Section */}
                  <div className="flex-1 flex flex-col min-h-0">
                    {/* File Tabs */}
                    <FileTabs
                      openFiles={openFiles}
                      activeFile={activeFile}
                      onFileSelect={setActiveFile}
                      onFileClose={handleFileClose}
                      onFileCloseAll={handleFileCloseAll}
                      onFileCloseOthers={handleFileCloseOthers}
                      unsavedFiles={unsavedFiles}
                    />

                    {/* Code Editor */}
                    <div className="flex-1 min-h-0">
                      {activeFile ? (
                        <CodeEditor
                          value={currentFileContent}
                          onChange={handleFileChange}
                          fileName={activeFile}
                          onSave={handleFileSave}
                          onRun={handleRunCode}
                          onAIAssist={handleAIAssist}
                          className="h-full"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full text-gray-400">
                          <div className="text-center">
                            <Code className="w-16 h-16 mx-auto mb-4 opacity-30" />
                            <p className="text-sm">Open a file to start editing</p>
                            <p className="text-xs text-gray-500 mt-1">Use the file explorer or create a new file</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Terminal Panel - Integrated underneath editor */}
                  <AnimatePresence>
                    {showTerminal && (
                      <>
                        {/* Terminal Resize Handle */}
                        {!isMobile && (
                          <div
                            className={`h-1 bg-transparent hover:bg-white/10 cursor-row-resize resize-handle ${
                              isResizingTerminal ? 'bg-white/20' : ''
                            }`}
                            onMouseDown={handleTerminalResize}
                          />
                        )}

                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: terminalHeight, opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: [0.23, 1, 0.32, 1] }}
                          className="flex-shrink-0 border-t border-white/08"
                          style={{ height: terminalHeight }}
                        >
                          <div className="h-full flex flex-col">
                            {/* Terminal Header */}
                            <div className="px-3 py-2 border-b border-white/08 flex items-center justify-between bg-gray-800/50">
                              <div className="flex items-center gap-2">
                                <TerminalIcon className="w-4 h-4 text-gray-400" />
                                <span className="text-xs font-medium text-gray-300">TERMINAL</span>
                              </div>
                              <button
                                onClick={() => setShowTerminal(false)}
                                className="ide-button w-5 h-5 flex items-center justify-center"
                                title="Close Terminal"
                              >
                                <span className="text-xs">×</span>
                              </button>
                            </div>

                            {/* Terminal Content */}
                            <div className="flex-1 min-h-0">
                              <WebTerminal
                                onCommand={handleTerminalCommand}
                                className="h-full"
                                title=""
                                fileStructure={fileStructure}
                                onFileStructureUpdate={handleFileStructureUpdate}
                              />
                            </div>
                          </div>
                        </motion.div>
                      </>
                    )}
                  </AnimatePresence>
                </motion.div>
              ) : (
                <motion.div
                  key="preview"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="flex-1 ide-panel"
                >
                  <div className="h-full flex flex-col">
                    {/* Preview Header */}
                    <div className="px-4 py-2 border-b border-white/08">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-400 rounded-full" />
                        <span className="text-xs font-medium text-gray-300">LIVE PREVIEW</span>
                      </div>
                    </div>

                    {/* Preview Content */}
                    <div className="flex-1 scrollable-panel rounded-lg">
                      <LivePreview
                        fileStructure={fileStructure}
                        activeFile={activeFile}
                        className="h-full w-full"
                      />
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Chat Sidebar - Right Side */}
        <AnimatePresence>
          {showChatSidebar && (
            <>
              {/* Chat Resize Handle */}
              {!isMobile && (
                <div
                  className={`w-1 bg-transparent hover:bg-white/10 cursor-col-resize resize-handle ${
                    isResizingChat ? 'bg-white/20' : ''
                  }`}
                  onMouseDown={handleChatResize}
                />
              )}
              
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: chatSidebarWidth, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.3, ease: [0.23, 1, 0.32, 1] }}
                className="flex-shrink-0 ide-panel border-l border-white/08"
              >
                <div className="h-full flex flex-col">
                  {/* Chat Header */}
                  <div className="px-3 py-2 border-b border-white/08">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <MessageSquare className="w-4 h-4 text-blue-400" />
                        <span className="text-xs font-medium text-gray-300">AI ASSISTANT</span>
                      </div>
                      <button
                        onClick={() => setShowChatSidebar(false)}
                        className="ide-button w-5 h-5 flex items-center justify-center"
                        title="Close Chat"
                      >
                        <span className="text-xs">×</span>
                      </button>
                    </div>
                  </div>
                  
                  {/* Chat Content */}
                  <div className="flex-1 scrollable-panel">
                    <AIChatAssistant
                      fileStructure={fileStructure}
                      activeFile={activeFile}
                      onCodeGenerated={handleFileStructureUpdate}
                      onFileUpdate={handleFileChange}
                      className="h-full"
                    />
                  </div>
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default IDELayout;
